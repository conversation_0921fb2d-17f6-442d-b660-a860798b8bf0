#!/usr/bin/env python3
"""
Test script to verify that all imports and basic functionality work correctly.
"""

def test_imports():
    """Test that all required imports work."""
    try:
        print("Testing imports...")
        
        # Test basic Python imports
        import os
        import sys
        import time
        import traceback
        from datetime import datetime
        from typing import List, Union, Optional, Tuple
        print("✅ Basic Python imports successful")
        
        # Test Airtest imports
        from airtest.core.api import (
            connect_device, exists, touch, swipe, snapshot, log
        )
        from airtest.core.settings import Settings as ST
        from airtest.core.error import TargetNotFoundError
        from airtest.core.cv import Template
        print("✅ Airtest imports successful")
        
        # Test that we can create a Template object
        test_template = Template("test.png", threshold=0.8, rgb=False)
        print("✅ Template creation successful")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_main_script():
    """Test that the main script can be imported without errors."""
    try:
        print("\nTesting main script import...")
        
        # This will test if the script has any syntax errors
        import auto_doaxvv_festival
        print("✅ Main script import successful")
        
        # Test that key functions exist
        functions_to_check = [
            'setup_logging',
            'setup_templates', 
            'validate_templates',
            'robust_wait_and_touch',
            'handle_unexpected_popups',
            'scroll_to_find_festival',
            'check_fp_status',
            'navigate_to_festival_selection',
            'main'
        ]
        
        for func_name in functions_to_check:
            if hasattr(auto_doaxvv_festival, func_name):
                print(f"✅ Function {func_name} found")
            else:
                print(f"❌ Function {func_name} missing")
                return False
        
        print("✅ All required functions found")
        return True
        
    except Exception as e:
        print(f"❌ Error importing main script: {e}")
        return False

if __name__ == "__main__":
    import sys

    print("DOAXVV Festival Automation - Import Test")
    print("=" * 50)

    success = True

    # Test imports
    if not test_imports():
        success = False

    # Test main script
    if not test_main_script():
        success = False

    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("The automation script should work correctly.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the error messages above.")

    sys.exit(0 if success else 1)
