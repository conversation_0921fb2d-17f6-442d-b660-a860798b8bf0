#!/usr/bin/env python
# -*- coding: utf-8 -*-

from __future__ import unicode_literals


ELEMENTS = [
    'Any',
    'Other',
    'Application',
    'Group',
    'Window',
    'Sheet',
    'Drawer',
    '<PERSON><PERSON>',
    'Dialog',
    'But<PERSON>',
    'RadioButton',
    'RadioGroup',
    'CheckBox',
    'DisclosureTriangle',
    'PopUpButton',
    'ComboBox',
    'MenuButton',
    'ToolbarButton',
    'Popover',
    'Keyboard',
    'Key',
    'NavigationBar',
    'TabBar',
    'TabGroup',
    'Toolbar',
    'StatusBar',
    'Table',
    'TableRow',
    'TableColumn',
    'Outline',
    'OutlineRow',
    'Browser',
    'CollectionView',
    'Slider',
    'PageIndicator',
    'ProgressIndicator',
    'ActivityIndicator',
    'SegmentedControl',
    'Picker',
    'PickerWheel',
    'Switch',
    'Toggle',
    'Link',
    'Image',
    'Icon',
    'SearchField',
    'ScrollView',
    '<PERSON>rollBar',
    'StaticText',
    'TextField',
    'SecureTextField',
    'DatePicker',
    'TextView',
    'Menu',
    'MenuItem',
    'MenuBar',
    'MenuBarItem',
    'Map',
    'WebView',
    'IncrementArrow',
    'DecrementArrow',
    'Timeline',
    'RatingIndicator',
    'ValueIndicator',
    'SplitGroup',
    'Splitter',
    'RelevanceIndicator',
    'ColorWell',
    'HelpTag',
    'Matte',
    'DockItem',
    'Ruler',
    'RulerMarker',
    'Grid',
    'LevelIndicator',
    'Cell',
    'LayoutArea',
    'LayoutItem',
    'Handle',
    'Stepper',
    'Tab'
]
