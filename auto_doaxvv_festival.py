#!/usr/bin/env python3
"""
DOAXVV Festival Mode Automation Script
=====================================

A comprehensive automation script for Dead or Alive Xtreme Venus Vacation (DOAXVV)
Festival mode using the Airtest framework. Designed for PC (Steam/DMM) versions.

Features:
- Robust template matching with multiple CV strategies
- Configurable FP management and drink usage
- Comprehensive error handling and logging
- Modular design for easy maintenance
- Support for visual variations and UI effects

Author: Augment Agent
Version: 1.0
"""

import os
import sys
import time
import traceback
from datetime import datetime
from typing import List, Union, Optional, Tuple

# Airtest imports
from airtest.core.api import (
    connect_device, exists, touch, swipe, snapshot, log
)
from airtest.core.settings import Settings as ST
from airtest.core.error import TargetNotFoundError
from airtest.core.cv import Template

# =============================================================================
# USER CONFIGURATION SECTION
# =============================================================================

# Connection Settings
DEVICE_STRING = "Windows:///"
GAME_WINDOW_TITLE_REGEX = r".*DOAXVV.*"

# Gameplay Settings
MAX_MATCHES = 50
USE_FP_DRINK = True
MAX_SCROLLS_TO_FIND_FESTIVAL = 5
MATCH_TIMEOUT = 300  # Maximum time to wait for match completion (seconds)

# Template Matching Settings
DEFAULT_THRESHOLD = 0.8
TEMPLATE_RGB = False
CV_STRATEGIES = ["surf", "sift", "brisk", "tpl"]

# Timing Settings
DEFAULT_WAIT_TIMEOUT = 20
POPUP_CHECK_TIMEOUT = 5
RETRY_DELAY = 2
MAX_RETRIES = 3

# Directory Paths
IMAGES_DIR = "./images/"
LOGS_DIR = "./logs/"
CONFIG_DIR = "./config/"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Statistics tracking
matches_played = 0
script_start_time: Optional[datetime] = None
stop_reason = "Unknown"

# Template objects (will be initialized in setup_templates())
IMG_HOME_FESTIVAL_BUTTON: Optional[Template] = None
IMG_FESTIVAL_SELECTION_TITLE: Optional[Template] = None
IMG_TARGET_FESTIVAL: Optional[Template] = None
IMG_CHALLENGE_BUTTON: Optional[Template] = None
IMG_SKIP_BUTTON: Optional[Template] = None
IMG_NEXT_BUTTON: Optional[Template] = None
IMG_RESULTS_SCREEN: Optional[Template] = None
IMG_FP_EXHAUSTED_POPUP: Optional[Template] = None
IMG_USE_FP_DRINK_BUTTON: Optional[Template] = None
IMG_CONFIRM_FP_DRINK: Optional[Template] = None
IMG_CLOSE_BUTTON: Optional[Template] = None
IMG_BACK_BUTTON: Optional[Template] = None

# Common popup templates
IMG_LOGIN_BONUS_POPUP: Optional[Template] = None
IMG_MAINTENANCE_POPUP: Optional[Template] = None
IMG_CONNECTION_ERROR_POPUP: Optional[Template] = None
IMG_OK_BUTTON: Optional[Template] = None
IMG_CANCEL_BUTTON: Optional[Template] = None

def setup_logging():
    """Initialize Airtest logging and create log directories."""
    global script_start_time
    script_start_time = datetime.now()
    
    # Create directories if they don't exist
    os.makedirs(LOGS_DIR, exist_ok=True)
    os.makedirs(IMAGES_DIR, exist_ok=True)
    os.makedirs(CONFIG_DIR, exist_ok=True)
    
    # Set up Airtest logging
    timestamp = script_start_time.strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(LOGS_DIR, f"doaxvv_festival_{timestamp}.html")
    
    # Configure Airtest settings
    try:
        ST.LOG_DIR = LOGS_DIR
        ST.LOG_FILE = log_file
        ST.CVSTRATEGY = CV_STRATEGIES
        ST.THRESHOLD = DEFAULT_THRESHOLD
        ST.OPDELAY = 1  # Delay between operations
    except Exception as e:
        print(f"Warning: Could not configure all Airtest settings: {e}")
        # Continue anyway as these are not critical for basic functionality
    
    log(f"DOAXVV Festival Automation Started at {script_start_time}")
    log(f"Configuration: MAX_MATCHES={MAX_MATCHES}, USE_FP_DRINK={USE_FP_DRINK}")

def setup_templates():
    """Initialize all template objects with proper settings."""
    global IMG_HOME_FESTIVAL_BUTTON, IMG_FESTIVAL_SELECTION_TITLE, IMG_TARGET_FESTIVAL
    global IMG_CHALLENGE_BUTTON, IMG_SKIP_BUTTON, IMG_NEXT_BUTTON, IMG_RESULTS_SCREEN
    global IMG_FP_EXHAUSTED_POPUP, IMG_USE_FP_DRINK_BUTTON, IMG_CONFIRM_FP_DRINK
    global IMG_CLOSE_BUTTON, IMG_BACK_BUTTON, IMG_LOGIN_BONUS_POPUP, IMG_MAINTENANCE_POPUP
    global IMG_CONNECTION_ERROR_POPUP, IMG_OK_BUTTON, IMG_CANCEL_BUTTON
    
    # Main navigation templates
    IMG_HOME_FESTIVAL_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "home_festival_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB,
        record_pos=(0.5, 0.3)  # Approximate position for reference
    )
    
    IMG_FESTIVAL_SELECTION_TITLE = Template(
        os.path.join(IMAGES_DIR, "festival", "festival_selection_title.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_TARGET_FESTIVAL = Template(
        os.path.join(IMAGES_DIR, "festival", "target_festival_event.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    # Action buttons
    IMG_CHALLENGE_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "challenge_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_SKIP_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "skip_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_NEXT_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "next_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    # Result and status screens
    IMG_RESULTS_SCREEN = Template(
        os.path.join(IMAGES_DIR, "festival", "match_results_screen.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    # FP management templates
    IMG_FP_EXHAUSTED_POPUP = Template(
        os.path.join(IMAGES_DIR, "popups", "fp_exhausted_popup.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_USE_FP_DRINK_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "use_fp_drink_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_CONFIRM_FP_DRINK = Template(
        os.path.join(IMAGES_DIR, "popups", "confirm_fp_drink_popup.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    # Common UI elements
    IMG_CLOSE_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "close_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_BACK_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "back_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_OK_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "ok_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_CANCEL_BUTTON = Template(
        os.path.join(IMAGES_DIR, "buttons", "cancel_button.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    # Popup templates
    IMG_LOGIN_BONUS_POPUP = Template(
        os.path.join(IMAGES_DIR, "popups", "login_bonus_popup.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_MAINTENANCE_POPUP = Template(
        os.path.join(IMAGES_DIR, "popups", "maintenance_popup.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    IMG_CONNECTION_ERROR_POPUP = Template(
        os.path.join(IMAGES_DIR, "popups", "connection_error_popup.png"),
        threshold=DEFAULT_THRESHOLD, rgb=TEMPLATE_RGB
    )
    
    log("Template objects initialized successfully")

def validate_templates() -> bool:
    """
    Validate that all critical templates are properly initialized.

    Returns:
        bool: True if all critical templates are available, False otherwise
    """
    critical_templates = [
        ("IMG_HOME_FESTIVAL_BUTTON", IMG_HOME_FESTIVAL_BUTTON),
        ("IMG_FESTIVAL_SELECTION_TITLE", IMG_FESTIVAL_SELECTION_TITLE),
        ("IMG_TARGET_FESTIVAL", IMG_TARGET_FESTIVAL),
        ("IMG_CHALLENGE_BUTTON", IMG_CHALLENGE_BUTTON),
        ("IMG_SKIP_BUTTON", IMG_SKIP_BUTTON),
        ("IMG_NEXT_BUTTON", IMG_NEXT_BUTTON)
    ]

    missing_templates = []
    for name, template in critical_templates:
        if template is None:
            missing_templates.append(name)
        else:
            # Check if template file exists
            try:
                if not os.path.exists(template.filename):
                    missing_templates.append(f"{name} (file not found: {template.filename})")
            except AttributeError:
                # Template might not have filename attribute in some versions
                pass

    if missing_templates:
        log(f"Critical templates missing or invalid: {', '.join(missing_templates)}")
        return False

    log("All critical templates validated successfully")
    return True

def robust_wait_and_touch(template_or_list: Union[Template, List[Template], Optional[Template], List[Optional[Template]]],
                         timeout: int = DEFAULT_WAIT_TIMEOUT,
                         threshold_override: Optional[float] = None) -> bool:
    """
    Wait for template(s) and click the first one found.

    Args:
        template_or_list: Single template or list of templates to search for
        timeout: Maximum time to wait in seconds
        threshold_override: Override default threshold for this search

    Returns:
        bool: True if template found and clicked, False otherwise
    """
    templates: List[Template] = []
    original_thresholds: List[float] = []

    try:
        # Handle different input types and filter out None values
        if isinstance(template_or_list, list):
            templates = [t for t in template_or_list if t is not None]
        elif template_or_list is not None:
            templates = [template_or_list]

        # If no valid templates, return False
        if not templates:
            log("No valid templates provided to robust_wait_and_touch")
            return False

        # Override threshold if specified
        if threshold_override:
            for template in templates:
                original_thresholds.append(template.threshold)
                template.threshold = threshold_override

        start_time = time.time()
        while time.time() - start_time < timeout:
            for i, template in enumerate(templates):
                try:
                    if exists(template):
                        touch(template)
                        log(f"Successfully found and clicked template {i+1}")
                        return True
                except TargetNotFoundError:
                    continue

            time.sleep(0.5)  # Brief pause between checks

        log(f"Timeout reached ({timeout}s) - no templates found")
        return False

    except Exception as e:
        log(f"Error in robust_wait_and_touch: {str(e)}")
        return False

    finally:
        # Restore original thresholds
        if threshold_override and original_thresholds and templates:
            for i, template in enumerate(templates):
                if i < len(original_thresholds):
                    template.threshold = original_thresholds[i]

def handle_unexpected_popups() -> bool:
    """
    Check for and dismiss common popups that might interrupt automation.

    Returns:
        bool: True if popup was found and handled, False otherwise
    """
    popup_templates = [
        (IMG_LOGIN_BONUS_POPUP, "Login bonus popup"),
        (IMG_MAINTENANCE_POPUP, "Maintenance popup"),
        (IMG_CONNECTION_ERROR_POPUP, "Connection error popup")
    ]

    for popup_template, popup_name in popup_templates:
        try:
            if popup_template is not None and exists(popup_template):
                log(f"Found {popup_name} - attempting to dismiss")

                # Try to find and click OK/Close button
                if robust_wait_and_touch([IMG_OK_BUTTON, IMG_CLOSE_BUTTON], timeout=5):
                    log(f"Successfully dismissed {popup_name}")
                    time.sleep(1)  # Brief pause after dismissing
                    return True
                else:
                    log(f"Could not find dismiss button for {popup_name}")

        except (TargetNotFoundError, Exception) as e:
            log(f"Error checking for {popup_name}: {str(e)}")
            continue

    return False

def scroll_to_find_festival(target_template: Optional[Template], max_scrolls: int = MAX_SCROLLS_TO_FIND_FESTIVAL) -> bool:
    """
    Scroll through festival list to find the target event.

    Args:
        target_template: Template of the festival event to find
        max_scrolls: Maximum number of scroll attempts

    Returns:
        bool: True if festival found, False otherwise
    """
    if target_template is None:
        log("Target template is None - cannot search for festival")
        return False

    log(f"Searching for target festival with up to {max_scrolls} scroll attempts")

    # First check if it's already visible
    try:
        if exists(target_template):
            log("Target festival found without scrolling")
            return True
    except TargetNotFoundError:
        pass

    # Scroll down to search for the festival
    for scroll_attempt in range(max_scrolls):
        log(f"Scroll attempt {scroll_attempt + 1}/{max_scrolls}")

        # Perform scroll down gesture (adjust coordinates based on your screen)
        swipe((500, 400), (500, 200), duration=1)
        time.sleep(1)  # Wait for scroll animation

        try:
            if exists(target_template):
                log(f"Target festival found after {scroll_attempt + 1} scrolls")
                return True
        except TargetNotFoundError:
            continue

    log(f"Target festival not found after {max_scrolls} scroll attempts")
    return False

def check_fp_status() -> bool:
    """
    Check if FP is exhausted and handle FP drink usage if configured.

    Returns:
        bool: True if FP is available (or was restored), False if exhausted
    """
    try:
        if IMG_FP_EXHAUSTED_POPUP is not None and exists(IMG_FP_EXHAUSTED_POPUP):
            log("FP exhausted popup detected")

            if USE_FP_DRINK:
                log("Attempting to use FP drink")

                # Look for FP drink usage button
                if robust_wait_and_touch(IMG_USE_FP_DRINK_BUTTON, timeout=5):
                    log("Clicked FP drink button")

                    # Confirm FP drink usage
                    if robust_wait_and_touch(IMG_CONFIRM_FP_DRINK, timeout=10):
                        log("Confirmed FP drink usage")

                        # Wait for confirmation and close any result popup
                        time.sleep(2)
                        robust_wait_and_touch([IMG_OK_BUTTON, IMG_CLOSE_BUTTON], timeout=5)

                        log("FP restored successfully")
                        return True
                    else:
                        log("Could not confirm FP drink usage")
                else:
                    log("FP drink button not found - may be out of drinks")
            else:
                log("FP drink usage disabled in configuration")

            # If we can't use FP drink or it failed, close the popup
            robust_wait_and_touch([IMG_CLOSE_BUTTON, IMG_CANCEL_BUTTON], timeout=5)
            return False

    except (TargetNotFoundError, Exception) as e:
        # No FP exhausted popup means FP is available, or there was an error
        log(f"FP status check completed (no popup found or error: {str(e)})")
        return True

    return True

def navigate_to_festival_selection() -> bool:
    """
    Navigate from home screen to festival selection screen.

    Returns:
        bool: True if successfully navigated, False otherwise
    """
    log("Navigating to festival selection")

    # Handle any unexpected popups first
    handle_unexpected_popups()

    # Look for and click the festival button from home screen
    if not robust_wait_and_touch(IMG_HOME_FESTIVAL_BUTTON, timeout=15):
        log("Could not find festival button on home screen")
        return False

    log("Clicked festival button")
    time.sleep(2)  # Wait for navigation

    # Verify we reached festival selection screen
    if robust_wait_and_touch(IMG_FESTIVAL_SELECTION_TITLE, timeout=10):
        log("Successfully reached festival selection screen")
        return True
    else:
        log("Failed to reach festival selection screen")
        return False

def start_festival_match() -> bool:
    """
    Start a festival match from the festival selection screen.

    Returns:
        bool: True if match started successfully, False otherwise
    """
    log("Starting festival match")

    # Check FP status before attempting to start match
    if not check_fp_status():
        global stop_reason
        stop_reason = "FP exhausted"
        return False

    # Find and click the target festival
    if not scroll_to_find_festival(IMG_TARGET_FESTIVAL):
        log("Could not find target festival")
        return False

    # Click on the target festival
    if not robust_wait_and_touch(IMG_TARGET_FESTIVAL, timeout=10):
        log("Could not click target festival")
        return False

    log("Clicked target festival")
    time.sleep(2)  # Wait for festival details to load

    # Click challenge/confirmation button
    if not robust_wait_and_touch(IMG_CHALLENGE_BUTTON, timeout=15):
        log("Could not find challenge button")
        return False

    log("Clicked challenge button - match should be starting")
    return True

def handle_match_execution() -> bool:
    """
    Handle the actual match execution (skip and wait for results).

    Returns:
        bool: True if match completed successfully, False otherwise
    """
    log("Handling match execution")

    # Wait for skip button to appear (indicates match has started)
    log("Waiting for skip button to appear...")
    if not robust_wait_and_touch(IMG_SKIP_BUTTON, timeout=MATCH_TIMEOUT):
        log("Skip button not found - match may not have started properly")
        return False

    log("Found and clicked skip button")
    time.sleep(3)  # Wait for skip to process

    # Wait for results screen and handle it
    log("Waiting for match results...")
    result_handled = False
    attempts = 0
    max_result_attempts = 10

    while not result_handled and attempts < max_result_attempts:
        attempts += 1
        log(f"Result handling attempt {attempts}/{max_result_attempts}")

        # Look for results screen or next button
        results_found = False
        try:
            if (IMG_RESULTS_SCREEN is not None and exists(IMG_RESULTS_SCREEN)) or \
               (IMG_NEXT_BUTTON is not None and exists(IMG_NEXT_BUTTON)):
                results_found = True
        except Exception as e:
            log(f"Error checking for results screen: {str(e)}")

        if results_found:
            log("Found results screen or next button")

            # Click next button to proceed
            if robust_wait_and_touch(IMG_NEXT_BUTTON, timeout=5):
                log("Clicked next button")
                time.sleep(2)

                # Check if we're back to festival selection or need more clicks
                try:
                    if IMG_FESTIVAL_SELECTION_TITLE is not None and exists(IMG_FESTIVAL_SELECTION_TITLE):
                        log("Successfully returned to festival selection")
                        result_handled = True
                    else:
                        # May need additional next button clicks
                        continue
                except TargetNotFoundError:
                    continue
            else:
                # Try alternative buttons if next button not found
                if robust_wait_and_touch([IMG_OK_BUTTON, IMG_CLOSE_BUTTON], timeout=3):
                    log("Clicked alternative result button")
                    time.sleep(1)
                else:
                    time.sleep(2)  # Wait and try again
        else:
            time.sleep(2)  # Wait for results to appear

    if result_handled:
        log("Match results handled successfully")
        return True
    else:
        log("Failed to handle match results properly")
        return False

def execute_single_match() -> bool:
    """
    Execute a complete single match cycle.

    Returns:
        bool: True if match completed successfully, False otherwise
    """
    global matches_played

    log(f"=== Starting Match {matches_played + 1} ===")

    try:
        # Start the match
        if not start_festival_match():
            log("Failed to start festival match")
            return False

        # Handle match execution
        if not handle_match_execution():
            log("Failed to handle match execution")
            return False

        # Increment match counter
        matches_played += 1
        log(f"Match {matches_played} completed successfully")

        # Brief pause between matches
        time.sleep(2)

        return True

    except Exception as e:
        log(f"Error during match execution: {str(e)}")
        log(f"Exception traceback: {traceback.format_exc()}")
        return False

def connect_to_game() -> bool:
    """
    Connect to the DOAXVV game window.

    Returns:
        bool: True if connection successful, False otherwise
    """
    try:
        log("Attempting to connect to DOAXVV game window")

        # Connect to Windows device
        connect_device(DEVICE_STRING)
        log(f"Connected to device: {DEVICE_STRING}")

        # Try to find and focus the game window
        # Note: This may require adjustment based on actual window title
        log("Looking for DOAXVV game window...")

        # Take a screenshot to verify connection
        screenshot_path = snapshot(msg="Initial connection screenshot")
        log(f"Connection screenshot saved: {screenshot_path}")

        return True

    except Exception as e:
        log(f"Failed to connect to game: {str(e)}")
        return False

def print_execution_summary():
    """Print a summary of the automation execution."""
    global script_start_time, matches_played, stop_reason

    end_time = datetime.now()

    # Handle case where script_start_time might be None
    if script_start_time is not None:
        duration = end_time - script_start_time
        start_time_str = script_start_time.strftime('%Y-%m-%d %H:%M:%S')
    else:
        duration = "Unknown"
        start_time_str = "Unknown"

    print("\n" + "="*50)
    print("DOAXVV FESTIVAL AUTOMATION SUMMARY")
    print("="*50)
    print(f"Start Time: {start_time_str}")
    print(f"End Time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Duration: {duration}")
    print(f"Matches Played: {matches_played}")
    print(f"Stop Reason: {stop_reason}")
    print(f"Max Matches Setting: {MAX_MATCHES}")
    print(f"FP Drink Usage: {'Enabled' if USE_FP_DRINK else 'Disabled'}")
    print("="*50)

    # Also log to Airtest log
    log(f"Automation completed - Matches: {matches_played}, Duration: {duration}, Reason: {stop_reason}")

def main():
    """
    Main execution function for the DOAXVV Festival automation.
    """
    global stop_reason

    try:
        # Initialize logging and setup
        setup_logging()
        setup_templates()

        # Validate templates before proceeding
        if not validate_templates():
            stop_reason = "Template validation failed"
            log("Template validation failed - aborting")
            print("ERROR: Critical template files are missing or invalid!")
            print("Please ensure all required template images are captured and saved.")
            print("See images/README.md for instructions.")
            return False

        log("DOAXVV Festival Automation Script Starting")
        print("DOAXVV Festival Automation Script")
        print("=" * 40)
        print(f"Max Matches: {MAX_MATCHES}")
        print(f"FP Drink Usage: {'Enabled' if USE_FP_DRINK else 'Disabled'}")
        print(f"Template Threshold: {DEFAULT_THRESHOLD}")
        print("=" * 40)

        # Connect to game
        if not connect_to_game():
            stop_reason = "Connection failed"
            log("Failed to connect to game - aborting")
            return False

        # Navigate to festival selection
        if not navigate_to_festival_selection():
            stop_reason = "Navigation failed"
            log("Failed to navigate to festival selection - aborting")
            return False

        # Main automation loop
        log("Starting main automation loop")
        consecutive_failures = 0
        max_consecutive_failures = 3

        while matches_played < MAX_MATCHES:
            log(f"Loop iteration - Matches played: {matches_played}/{MAX_MATCHES}")

            # Handle unexpected popups before each match
            handle_unexpected_popups()

            # Execute a single match
            if execute_single_match():
                consecutive_failures = 0  # Reset failure counter on success
                log(f"Match {matches_played} completed successfully")

                # Check if we've reached the maximum
                if matches_played >= MAX_MATCHES:
                    stop_reason = "Maximum matches reached"
                    log(f"Reached maximum matches ({MAX_MATCHES}) - stopping")
                    break

            else:
                consecutive_failures += 1
                log(f"Match failed - consecutive failures: {consecutive_failures}")

                if consecutive_failures >= max_consecutive_failures:
                    stop_reason = f"Too many consecutive failures ({consecutive_failures})"
                    log(f"Too many consecutive failures - stopping automation")
                    break

                # Try to recover by navigating back to festival selection
                log("Attempting to recover by navigating to festival selection")
                if not navigate_to_festival_selection():
                    stop_reason = "Recovery navigation failed"
                    log("Failed to recover - stopping automation")
                    break

        log("Main automation loop completed")
        return True

    except KeyboardInterrupt:
        stop_reason = "User interrupted (Ctrl+C)"
        log("Automation interrupted by user")
        return False

    except Exception as e:
        stop_reason = f"Unexpected error: {str(e)}"
        log(f"Unexpected error in main(): {str(e)}")
        log(f"Exception traceback: {traceback.format_exc()}")
        return False

    finally:
        # Always print summary
        print_execution_summary()

if __name__ == "__main__":
    """
    Entry point for the script.
    """
    try:
        # Check if required directories exist, create them if not
        for directory in [IMAGES_DIR, LOGS_DIR, CONFIG_DIR]:
            if not os.path.exists(directory):
                print(f"Creating directory: {directory}")
                os.makedirs(directory, exist_ok=True)

        # Run the main automation
        success = main()

        if success:
            print("\nAutomation completed successfully!")
            sys.exit(0)
        else:
            print(f"\nAutomation stopped: {stop_reason}")
            sys.exit(1)

    except Exception as e:
        print(f"\nFatal error: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)
